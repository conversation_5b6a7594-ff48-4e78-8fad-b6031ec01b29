<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Schedule MaxMind GeoLite2-Country database download every week
Artisan::command('maxmind:download --quiet', function () {
    $this->call('maxmind:download', ['--quiet' => true]);
})->purpose('Download MaxMind GeoLite2-Country database')->weekly();
