<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use PharData;

/**
 * Download MaxMind GeoLite2 Country Database Command
 *
 * This command downloads the latest MaxMind GeoLite2-Country database
 * using the MaxMind license key and stores it in the storage/maxmind directory.
 * The command handles:
 * - Downloading the compressed database file
 * - Extracting the .mmdb file from the tar.gz archive
 * - Replacing the existing database file
 * - Cleaning up temporary files
 * - Proper error handling and logging
 */
class DownloadMaxMindDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'maxmind:download
                            {--force : Force download even if file exists and is recent}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download the latest MaxMind GeoLite2-Country database';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $licenseKey = config('services.maxmind.license_key');

        if (empty($licenseKey)) {
            $this->error('MaxMind license key is not configured. Please set MAXMIND_LICENSE_KEY in your .env file.');

            return 1;
        }

        $storageDir = storage_path('maxmind');
        $databasePath = $storageDir.'/GeoLite2-Country.mmdb';

        // Check if we should skip download
        if (! $this->option('force') && $this->shouldSkipDownload($databasePath)) {
            if (! $this->option('quiet')) {
                $this->info('Database file is recent (less than 6 days old). Use --force to download anyway.');
            }

            return 0;
        }

        if (! $this->option('quiet')) {
            $this->info('Starting MaxMind GeoLite2-Country database download...');
        }

        try {
            // Ensure storage directory exists
            if (! File::exists($storageDir)) {
                File::makeDirectory($storageDir, 0755, true);
            }

            // Download the database
            $tempFile = $this->downloadDatabase($licenseKey);

            // Extract and install the database
            $this->extractAndInstallDatabase($tempFile, $databasePath);

            // Clean up temporary file
            if (File::exists($tempFile)) {
                File::delete($tempFile);
            }

            if (! $this->option('quiet')) {
                $this->info('✓ MaxMind GeoLite2-Country database updated successfully!');
                $this->info("Database location: {$databasePath}");

                if (File::exists($databasePath)) {
                    $size = File::size($databasePath);
                    $this->info('Database size: '.$this->formatBytes($size));
                }
            }

            Log::info('MaxMind GeoLite2-Country database updated successfully', [
                'database_path' => $databasePath,
                'file_size' => File::exists($databasePath) ? File::size($databasePath) : 0,
            ]);

            return 0;

        } catch (\Exception $e) {
            $this->error('Failed to download MaxMind database: '.$e->getMessage());
            Log::error('MaxMind database download failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * Check if we should skip the download based on file age
     */
    private function shouldSkipDownload(string $databasePath): bool
    {
        if (! File::exists($databasePath)) {
            return false;
        }

        $fileAge = time() - File::lastModified($databasePath);
        $sixDaysInSeconds = 6 * 24 * 60 * 60;

        return $fileAge < $sixDaysInSeconds;
    }

    /**
     * Download the MaxMind database file
     */
    private function downloadDatabase(string $licenseKey): string
    {
        $downloadUrl = config('services.maxmind.download_url');
        $edition = config('services.maxmind.database_edition');
        $suffix = config('services.maxmind.database_suffix');

        $tempFile = storage_path('maxmind/temp_'.time().'.tar.gz');

        if (! $this->option('quiet')) {
            $this->info('Downloading database from MaxMind...');
        }

        $response = Http::timeout(300)
            ->withOptions(['verify' => true])
            ->get($downloadUrl, [
                'edition_id' => $edition,
                'license_key' => $licenseKey,
                'suffix' => $suffix,
            ]);

        if (! $response->successful()) {
            throw new \Exception("Failed to download database. HTTP Status: {$response->status()}. Response: {$response->body()}");
        }

        // Check if response is actually a tar.gz file
        $contentType = $response->header('Content-Type');
        if (! str_contains($contentType, 'application/gzip') && ! str_contains($contentType, 'application/x-gzip')) {
            // Check if it's an error response
            $body = $response->body();
            if (str_contains($body, 'Invalid license key') || str_contains($body, 'error')) {
                throw new \Exception("MaxMind API error: {$body}");
            }
        }

        File::put($tempFile, $response->body());

        if (! File::exists($tempFile) || File::size($tempFile) === 0) {
            throw new \Exception('Downloaded file is empty or could not be saved');
        }

        return $tempFile;
    }

    /**
     * Extract the .mmdb file from the tar.gz archive and install it
     */
    private function extractAndInstallDatabase(string $tempFile, string $databasePath): void
    {
        if (! $this->option('quiet')) {
            $this->info('Extracting database file...');
        }

        $tempDir = storage_path('maxmind/temp_extract_'.time());

        try {
            // Create temporary extraction directory
            File::makeDirectory($tempDir, 0755, true);

            // Extract tar.gz file
            $phar = new PharData($tempFile);
            $phar->extractTo($tempDir);

            // Find the .mmdb file in the extracted contents
            $mmdbFile = $this->findMmdbFile($tempDir);

            if (! $mmdbFile) {
                throw new \Exception('Could not find .mmdb file in the downloaded archive');
            }

            // Move the .mmdb file to the final location
            File::move($mmdbFile, $databasePath);

        } finally {
            // Clean up temporary extraction directory
            if (File::exists($tempDir)) {
                File::deleteDirectory($tempDir);
            }
        }
    }

    /**
     * Recursively find the .mmdb file in the extracted directory
     */
    private function findMmdbFile(string $directory): ?string
    {
        $files = File::allFiles($directory);

        foreach ($files as $file) {
            if ($file->getExtension() === 'mmdb') {
                return $file->getPathname();
            }
        }

        return null;
    }

    /**
     * Format bytes into human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2).' '.$units[$pow];
    }
}
